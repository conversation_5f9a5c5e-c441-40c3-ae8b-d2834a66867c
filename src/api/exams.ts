import { apiClient } from '@/lib/api-client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

export type ExamFormData = {
  name: string;
  subject: string;
  course_id: string;
  level_id: string;
  type: string;
  date: string;
  time: string;
  class: string;
  status?: string;
};

export const getExams = async () => {
  try {
    console.log('Fetching exams from Firebase');
    const exams = await examModel.getAllExams();
    
    // Sort exams by date (newest first)
    return exams.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  } catch (error) {
    console.error('Error fetching exams:', error);
    throw error;
  }
};

export const getExamById = async (id: string) => {
  try {
    console.log(`Fetching exam with ID: ${id}`);
    const exam = await examModel.getExamById(id);
    
    if (!exam) {
      throw new Error('Exam not found');
    }
    
    return exam;
  } catch (error) {
    console.error('Error fetching exam:', error);
    throw error;
  }
};

export const createExam = async (examData: ExamFormData) => {
  try {
    console.log('Creating new exam:', examData);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Set default status if not provided
    const status = examData.status || 'upcoming';
    
    const examId = await examModel.createExam({
      ...examData,
      status
    });
    
    await logActivity('exam_created', { 
      examId,
      name: examData.name,
      courseId: examData.course_id,
      levelId: examData.level_id
    });
    
    toast.success('Exam created successfully');
    
    return {
      id: examId,
      ...examData,
      status
    };
  } catch (error: any) {
    console.error('Error creating exam:', error);
    toast.error(error.message || 'Failed to create exam');
    throw error;
  }
};

export const updateExam = async (id: string, examData: Partial<ExamFormData>) => {
  try {
    console.log('Updating exam:', id, examData);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    await examModel.updateExam(id, examData);
    
    await logActivity('exam_updated', {
      examId: id,
      updates: examData
    });
    
    toast.success('Exam updated successfully');
    
    return {
      id,
      ...examData
    };
  } catch (error: any) {
    console.error('Error updating exam:', error);
    toast.error(error.message || 'Failed to update exam');
    throw error;
  }
};

export const deleteExam = async (id: string) => {
  try {
    console.log('Deleting exam:', id);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Check if there are any results for this exam
    const results = await examResultModel.getExamResultsByExamId(id);
    
    if (results.length > 0) {
      throw new Error('Cannot delete exam with existing results');
    }
    
    await examModel.deleteExam(id);
    
    await logActivity('exam_deleted', {
      examId: id
    });
    
    toast.success('Exam deleted successfully');
    
    return { success: true };
  } catch (error: any) {
    console.error('Error deleting exam:', error);
    toast.error(error.message || 'Failed to delete exam');
    throw error;
  }
};

export const getStudentsForExam = async (examId: string) => {
  try {
    console.log(`Fetching students for exam with ID: ${examId}`);
    
    const exam = await examModel.getExamById(examId);
    
    if (!exam) {
      throw new Error('Exam not found');
    }
    
    // Get students for the course and level
    const students = await studentModel.getAllStudents({
      course_id: exam.course_id || '',
      level_id: exam.level_id || '',
      enrollment_status: 'Active'
    });
    
    // Get exam results for these students
    const results = await examResultModel.getExamResultsByExamId(examId);
    
    // Combine students with their results
    return students.map(student => {
      const result = results.find(r => r.student_id === student.id);
      return {
        ...student,
        examResult: result || null
      };
    });
  } catch (error) {
    console.error('Error fetching students for exam:', error);
    throw error;
  }
};

export const addExamResult = async (
  examId: string,
  studentId: string,
  marks: number,
  grade: string,
  remarks?: string
) => {
  try {
    console.log(`Adding exam result for student ${studentId} in exam ${examId}`);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Check if student and exam exist
    const student = await studentModel.getStudentById(studentId);
    const exam = await examModel.getExamById(examId);
    
    if (!student) {
      throw new Error('Student not found');
    }
    
    if (!exam) {
      throw new Error('Exam not found');
    }
    
    // Check if result already exists
    const existingResult = await examResultModel.getExamResultByExamAndStudentId(examId, studentId);
    
    if (existingResult) {
      // Update existing result
      await examResultModel.updateExamResult(existingResult.id, {
        marks,
        grade,
        remarks,
        date_recorded: new Date().toISOString(),
        recorded_by: user.uid
      });
      
      await logActivity('exam_result_updated', {
        examId,
        studentId,
        marks,
        grade
      });
      
      toast.success('Exam result updated successfully');
      
      return {
        id: existingResult.id,
        exam_id: examId,
        student_id: studentId,
        marks,
        grade,
        remarks,
        date_recorded: new Date().toISOString(),
        recorded_by: user.uid
      };
    } else {
      // Create new result
      const resultId = await examResultModel.createExamResult({
        exam_id: examId,
        student_id: studentId,
        marks,
        grade,
        remarks,
        date_recorded: new Date().toISOString(),
        recorded_by: user.uid
      });
      
      await logActivity('exam_result_created', {
        examId,
        studentId,
        marks,
        grade
      });
      
      toast.success('Exam result added successfully');
      
      return {
        id: resultId,
        exam_id: examId,
        student_id: studentId,
        marks,
        grade,
        remarks,
        date_recorded: new Date().toISOString(),
        recorded_by: user.uid
      };
    }
  } catch (error: any) {
    console.error('Error adding exam result:', error);
    toast.error(error.message || 'Failed to add exam result');
    throw error;
  }
};

export const bulkAddExamResults = async (
  examId: string,
  results: { studentId: string; marks: number; grade: string; remarks?: string }[]
) => {
  try {
    console.log(`Adding bulk exam results for exam ${examId}`);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Check if exam exists
    const exam = await examModel.getExamById(examId);
    
    if (!exam) {
      throw new Error('Exam not found');
    }
    
    const resultPromises = results.map(async (result) => {
      return addExamResult(
        examId,
        result.studentId,
        result.marks,
        result.grade,
        result.remarks
      );
    });
    
    await Promise.all(resultPromises);
    
    await logActivity('exam_results_bulk_created', {
      examId,
      count: results.length
    });
    
    toast.success(`Added ${results.length} exam results successfully`);
    
    return { success: true, count: results.length };
  } catch (error: any) {
    console.error('Error adding bulk exam results:', error);
    toast.error(error.message || 'Failed to add bulk exam results');
    throw error;
  }
}; 