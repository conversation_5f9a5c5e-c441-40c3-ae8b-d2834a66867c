// Script to verify courses and levels in Firestore
import * as dotenv from 'dotenv';
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs } from 'firebase/firestore';

// Load environment variables
dotenv.config();

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID,
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};

console.log('Using Firebase config:', {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function listData() {
  try {
    console.log('Fetching courses...');
    const courseSnapshot = await getDocs(collection(db, 'courses'));
    console.log(`Found ${courseSnapshot.size} courses:`);
    
    courseSnapshot.forEach(doc => {
      const course = doc.data();
      console.log(`- ${course.name} (${course.code})`);
    });
    
    console.log('\nFetching levels...');
    const levelSnapshot = await getDocs(collection(db, 'levels'));
    console.log(`Found ${levelSnapshot.size} levels:`);
    
    const levelsByCode = {};
    levelSnapshot.forEach(doc => {
      const level = doc.data();
      if (!levelsByCode[level.code]) {
        levelsByCode[level.code] = level;
      }
    });
    
    // Print just a sample of levels
    const levelCodes = Object.keys(levelsByCode);
    const sampleSize = Math.min(5, levelCodes.length);
    console.log(`Showing sample of ${sampleSize} levels:`);
    
    for (let i = 0; i < sampleSize; i++) {
      const level = levelsByCode[levelCodes[i]];
      console.log(`- ${level.name} (${level.code})`);
    }
    
    console.log('\nVerification completed successfully!');
  } catch (error) {
    console.error('Error verifying data:', error);
  }
}

// Execute the function
listData(); 