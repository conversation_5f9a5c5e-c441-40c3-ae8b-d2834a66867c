// <PERSON><PERSON><PERSON> to test updating a course directly in Firestore
import * as dotenv from 'dotenv';
import { initializeApp } from 'firebase/app';
import { getFirestore, doc, getDoc, updateDoc } from 'firebase/firestore';

// Load environment variables from different possible locations
try {
  // Try all possible .env file locations
  dotenv.config({ path: '../.env' });
  dotenv.config({ path: './.env' });
  dotenv.config({ path: '../.env.local' });
  dotenv.config({ path: './.env.local' });
} catch (error) {
  console.warn('Warning: Error loading environment variables:', error.message);
}

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID,
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Check if Firebase config is valid
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  console.error('Firebase configuration is incomplete. Please check your environment variables.');
  console.error('Required environment variables:');
  console.error('  - VITE_FIREBASE_API_KEY');
  console.error('  - VITE_FIREBASE_PROJECT_ID');
  process.exit(1);
}

console.log('Using Firebase config:', {
  projectId: firebaseConfig.projectId,
  apiKey: '******' // Hide API key in logs
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Function to test updating a course
async function testUpdateCourse() {
  if (process.argv.length < 3) {
    console.error('Please provide a course ID as an argument.');
    console.error('Usage: node test-course-edit.js COURSE_ID');
    process.exit(1);
  }

  const courseId = process.argv[2];
  console.log(`Testing update for course with ID: ${courseId}`);

  try {
    // First, get the current course data
    const courseRef = doc(db, 'courses', courseId);
    const courseSnap = await getDoc(courseRef);

    if (!courseSnap.exists()) {
      console.error(`Course with ID ${courseId} does not exist.`);
      process.exit(1);
    }

    const courseData = courseSnap.data();
    console.log('Current course data:', courseData);

    // Create a test update with a timestamp to ensure we can see the change
    const timestamp = new Date().toISOString();
    const updateData = {
      description: `${courseData.description} [Updated: ${timestamp}]`,
      updated_at: new Date()
    };

    console.log('Updating course with data:', updateData);

    // Perform the update
    await updateDoc(courseRef, updateData);
    console.log('Course update successful!');

    // Verify the update by re-fetching the course
    const updatedCourseSnap = await getDoc(courseRef);
    const updatedCourseData = updatedCourseSnap.data();
    console.log('Updated course data:', updatedCourseData);

    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error during test:', error);
    process.exit(1);
  }
}

// Execute the test function
console.log("====================================");
console.log("TESTING COURSE UPDATE IN FIREBASE");
console.log("====================================");

testUpdateCourse()
  .then(() => {
    console.log("====================================");
    console.log("Test completed successfully");
    console.log("====================================");
    process.exit(0);
  })
  .catch(error => {
    console.error("Test failed:", error);
    process.exit(1);
  }); 