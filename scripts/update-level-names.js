// <PERSON><PERSON>t to update existing level names to more specific educational level names
import * as dotenv from 'dotenv';
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, getDocs, updateDoc, query, where } from 'firebase/firestore';

// Load environment variables from different possible locations
try {
  // Try all possible .env file locations
  dotenv.config({ path: '../.env' });
  dotenv.config({ path: './.env' });
  dotenv.config({ path: '../.env.local' });
  dotenv.config({ path: './.env.local' });
} catch (error) {
  console.warn('Warning: Error loading environment variables:', error.message);
}

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID,
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Check if Firebase config is valid
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  console.error('Firebase configuration is incomplete. Please check your environment variables.');
  console.error('Required environment variables:');
  console.error('  - VITE_FIREBASE_API_KEY');
  console.error('  - VITE_FIREBASE_PROJECT_ID');
  process.exit(1);
}

console.log('Using Firebase config:', {
  projectId: firebaseConfig.projectId,
  apiKey: '******' // Hide API key in logs
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Level name mapping by course code
const levelNameMappings = {
  'IFTCLC': {
    'Level 1': { name: 'JSS 1 Female', code: 'IFTCLC-JSS1F', description: 'Beginner tailoring and computer skills for females' },
    'Level 2': { name: 'JSS 2 Female', code: 'IFTCLC-JSS2F', description: 'Intermediate tailoring and language skills for females' },
    'Level 3': { name: 'JSS 3 Female', code: 'IFTCLC-JSS3F', description: 'Advanced tailoring, computer and language skills for females' }
  },
  'IYA': {
    'Level 1': { name: 'JSS 1 Boys', code: 'IYA-JSS1B', description: 'Foundation level for junior secondary school boys' },
    'Level 2': { name: 'JSS 2 Boys', code: 'IYA-JSS2B', description: 'Intermediate level for junior secondary school boys' },
    'Level 3': { name: 'JSS 3 Boys', code: 'IYA-JSS3B', description: 'Advanced level for junior secondary school boys' },
    'Level 4': { name: 'JSS 1 Girls', code: 'IYA-JSS1G', description: 'Foundation level for junior secondary school girls' }
  },
  'HISTC': {
    'Level 1': { name: 'SSS Combined', code: 'HISTC-SSSC', description: 'Senior secondary school comprehensive program' }
  },
  'WC': {
    'Level 1': { name: 'Primary 1', code: 'WC-P1', description: 'Primary 1 weekend program' },
    'Level 2': { name: 'Primary 2', code: 'WC-P2', description: 'Primary 2 weekend program' },
    'Level 3': { name: 'Primary 3', code: 'WC-P3', description: 'Primary 3 weekend program' },
    'Level 4': { name: 'Primary 4', code: 'WC-P4', description: 'Primary 4 weekend program' },
    'Level 5': { name: 'Primary 5', code: 'WC-P5', description: 'Primary 5 weekend program' },
    'Level 6': { name: 'Primary 6', code: 'WC-P6', description: 'Primary 6 weekend program' }
  },
  'MEC': {
    'Level 1': { name: 'Tahfeez Beginner', code: 'MEC-TB', description: 'Foundation Islamic studies and basic Quran memorization' },
    'Level 2': { name: 'Tahfeez Intermediate', code: 'MEC-TI', description: 'Intermediate Islamic studies and continued Quran memorization' },
    'Level 3': { name: 'Tahfeez Advanced', code: 'MEC-TA', description: 'Advanced Islamic studies and comprehensive Quran memorization' },
    'Level 4': { name: 'Arabic Language', code: 'MEC-AL', description: 'Specialized Arabic language studies' }
  }
};

// Function to get course details by ID
async function getCourseDetails(courseId) {
  try {
    const courseDoc = await doc(db, 'courses', courseId);
    const courseSnap = await getDocs(query(collection(db, 'courses'), where('__name__', '==', courseId)));
    
    if (courseSnap.empty) {
      console.log(`Course with ID ${courseId} not found.`);
      return null;
    }
    
    return courseSnap.docs[0].data();
  } catch (error) {
    console.error(`Error getting course details for ${courseId}:`, error);
    return null;
  }
}

// Function to update level names
async function updateLevelNames() {
  console.log('Starting to update level names...');
  
  try {
    // Get all levels
    const levelsSnapshot = await getDocs(collection(db, 'levels'));
    
    if (levelsSnapshot.empty) {
      console.log('No levels found to update.');
      return;
    }
    
    console.log(`Found ${levelsSnapshot.size} levels to potentially update.`);
    
    const levelsToUpdate = [];
    
    // For each level, check if we need to update it
    for (const levelDoc of levelsSnapshot.docs) {
      const levelData = levelDoc.data();
      const courseId = levelData.course_id;
      
      if (!courseId) {
        console.log(`Level ${levelDoc.id} has no course_id. Skipping.`);
        continue;
      }
      
      // Get the course details
      const course = await getCourseDetails(courseId);
      
      if (!course) {
        console.log(`Could not find course for level ${levelDoc.id}. Skipping.`);
        continue;
      }
      
      const courseCode = course.code;
      
      // Check if we have mappings for this course
      if (!levelNameMappings[courseCode]) {
        console.log(`No mappings found for course ${courseCode}. Skipping.`);
        continue;
      }
      
      // Check if this level needs updating
      const currentLevelName = levelData.name;
      const mapping = levelNameMappings[courseCode][currentLevelName];
      
      if (!mapping) {
        console.log(`No mapping found for level "${currentLevelName}" in course ${courseCode}. Skipping.`);
        continue;
      }
      
      console.log(`Will update level "${currentLevelName}" to "${mapping.name}" in course ${courseCode}.`);
      
      levelsToUpdate.push({
        docId: levelDoc.id,
        oldName: currentLevelName,
        oldCode: levelData.code,
        newName: mapping.name,
        newCode: mapping.code,
        newDescription: mapping.description,
        courseCode: courseCode
      });
    }
    
    console.log(`Found ${levelsToUpdate.length} levels to update.`);
    
    // Now update each level
    for (const level of levelsToUpdate) {
      try {
        await updateDoc(doc(db, 'levels', level.docId), {
          name: level.newName,
          code: level.newCode,
          description: level.newDescription,
          updated_at: new Date()
        });
        
        console.log(`Successfully updated level "${level.oldName}" to "${level.newName}" (${level.docId}).`);
      } catch (error) {
        console.error(`Error updating level ${level.docId}:`, error);
      }
    }
    
    console.log(`Completed updating ${levelsToUpdate.length} levels.`);
  } catch (error) {
    console.error('Error updating level names:', error);
    throw error;
  }
}

// Execute the function
console.log("====================================");
console.log("UPDATING LEVEL NAMES IN FIREBASE");
console.log("====================================");

updateLevelNames()
  .then(() => {
    console.log("====================================");
    console.log("Script completed successfully");
    console.log("====================================");
    process.exit(0);
  })
  .catch(error => {
    console.error("Script failed:", error);
    process.exit(1);
  }); 