# Course and Level Management Scripts

This directory contains scripts for managing courses and levels in the database.

## Add Courses and Levels Script

The `add-courses-levels.ts` script adds multiple courses and their associated levels to the Firebase database.

### Prerequisites

- Node.js installed
- Firebase project configured
- `.env.local` file with Firebase credentials (same as used by the main application)

### Installation

Make sure you have the required packages installed:

```bash
npm install dotenv firebase
```

### Usage

1. Ensure your Firebase credentials are correctly set in your `.env.local` file:

```
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

2. Run the script using ts-node:

```bash
npx ts-node scripts/add-courses-levels.ts
```

Or compile and run:

```bash
npx tsc scripts/add-courses-levels.ts
node scripts/add-courses-levels.js
```

### Script Features

- Adds the following courses and their associated levels:
  - International Female Tailoring, Computer, and Language Classes (3 levels)
  - International Youth Academy (4 levels)
  - Higher Islamic Sciences and Technology Course (1 level)
  - Weekend Classes (6 levels)
  - Masjid Education Classes (4 levels)

- Each course includes:
  - Name
  - Code
  - Description
  - Duration
  - Fee
  - Status (active/inactive)

- Each level includes:
  - Name
  - Code
  - Description
  - Order (position in the course)
  - Associated course ID

- The script checks for existing courses by code before adding them to avoid duplicates.

### Modifying the Script

To add or modify courses and levels:

1. Edit the `coursesToAdd` array in the script.
2. Follow the existing structure for consistency.
3. Ensure each course has a unique code.
4. Ensure each level has a unique code within its course.

### Troubleshooting

- If the script fails to connect to Firebase, check your credentials in `.env.local`.
- If you encounter permission errors, ensure your Firebase account has write access to the database.
- For other issues, check the console output for specific error messages. 