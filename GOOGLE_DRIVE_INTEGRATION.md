# Google Drive Integration Implementation

## Overview

This document summarizes the implementation of Google Drive API integration to replace Firebase Storage for file uploads and downloads in the Academic Dashboard.

## Files Created/Modified

### New Files
1. `src/integrations/google/drive.ts` - Core Google Drive API integration module
2. `scripts/migrate-to-google-drive.js` - Migration script to move files from Firebase Storage to Google Drive
3. `GOOGLE_DRIVE_SETUP.md` - Documentation for setting up Google Drive API
4. `GOOGLE_DRIVE_INTEGRATION.md` - This summary document

### Modified Files
1. `.env` - Added Google Drive API credentials
2. `src/api/materials.ts` - Updated to use Google Drive instead of Firebase Storage
3. `src/integrations/firebase/models/material.ts` - Added file_id field for Google Drive files
4. `src/components/enrollment/components/StudentImageUpload.tsx` - Updated to use Google Drive instead of Supabase
5. `src/components/student/Assignments.tsx` - Updated to use Google Drive for assignment submissions
6. `src/integrations/firebase/models/submission.ts` - Added file_id field for Google Drive files
7. `src/api/student-assignments.ts` - Updated to handle file_id field
8. `package.json` - Added migration script

## Implementation Details

### Authentication & API Setup
- Implemented OAuth 2.0 authentication using service accounts
- Created a reusable Google Drive client initialization function
- Added environment variables for service account credentials

### File Organization
- Created a folder structure in Google Drive:
  - `profile_photos/` - For student profile photos
  - `materials/` - For educational materials
  - `submissions/{student_id}/{assignment_id}/` - For assignment submissions
- Implemented functions to create and manage folders

### File Upload & Retrieval
- Implemented file upload functionality with proper error handling
- Added public access permissions for uploaded files
- Generated and stored file IDs and URLs in Firestore
- Updated UI components to display files from Google Drive

### Migration
- Created a migration script to move existing files from Firebase Storage to Google Drive
- Added a npm script to run the migration: `npm run migrate-to-drive`

## Usage Instructions

### Setting Up Google Drive API
1. Follow the instructions in `GOOGLE_DRIVE_SETUP.md` to set up a Google Cloud project and service account
2. Add the required environment variables to your `.env` file

### Running the Migration
```bash
npm run migrate-to-drive
```

### Using the Google Drive API
```typescript
import { uploadFile, deleteFile, FOLDERS } from '@/integrations/google/drive';

// Upload a file
const uploadedFile = await uploadFile(
  file, // File, Blob, or Buffer
  fileName,
  FOLDERS.MATERIALS, // or any custom folder path
  file.type
);

// Get the file URL
const fileUrl = uploadedFile.webViewLink || uploadedFile.webContentLink;

// Store the file ID for later use
const fileId = uploadedFile.id;

// Delete a file
await deleteFile(fileId);
```

## Benefits of Google Drive Integration

1. **Higher Storage Limits**: Google Drive offers more storage compared to Firebase Storage free tier
2. **Better Organization**: Files are organized in folders for easier management
3. **Public Access**: Files are publicly accessible with shareable links
4. **Cost Efficiency**: Google Drive offers more cost-effective storage options for scaling

## Next Steps

1. Complete the migration of existing files using the migration script
2. Monitor storage usage and performance
3. Consider implementing additional features:
   - File versioning
   - Access control for sensitive files
   - Bulk file operations 